from odoo import models, fields, api, _

class AssetContract(models.Model):
    _name = 'asset.contract'
    _description = 'Asset Contract'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char('Nom du Contrat', required=True)
    contract_number = fields.Char('Numéro de Contrat')
    contract_type = fields.Selection([
        ('purchase', 'Achat'),
        ('lease', 'Location'),
        ('maintenance', 'Maintenance')
    ], required=True, string='Type de Contrat')
    supplier_id = fields.Many2one('res.partner', 'Fournisseur')
    contract_date = fields.Date('Date du Contrat')
    start_date = fields.Date('Date de Début')
    end_date = fields.Date('Date de Fin')
    total_amount = fields.Float('Montant Total')
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('under_review', 'En Révision'),
        ('approved', 'Approuvé'),
        ('active', 'Actif'),
        ('terminated', 'Résilié'),
        ('cancelled', 'Annulé')
    ], default='draft', string='État')
    
    # Relations
    asset_ids = fields.One2many('fixed.asset', 'contract_id', 'Immobilisations')
    clause_ids = fields.One2many('asset.contract.clause', 'contract_id', 'Clauses')
    verification_ids = fields.One2many('asset.contract.verification', 'contract_id', 'Vérifications')
    
    # Purchase validation fields
    purchase_validated = fields.Boolean('Achat Validé')
    purchase_validation_date = fields.Date('Date de Validation')
    purchase_validated_by = fields.Many2one('res.users', 'Validé par')
    is_verified = fields.Boolean('Vérifié', compute='_compute_is_verified')

    def _compute_is_verified(self):
        for contract in self:
            contract.is_verified = all(v.status == 'verified' for v in contract.verification_ids)

    def action_submit_for_review(self):
        self.state = 'under_review'

    def action_approve(self):
        self.state = 'approved'

    def action_activate(self):
        self.state = 'active'

    def action_validate_purchase(self):
        self.purchase_validated = True
        self.purchase_validation_date = fields.Date.today()
        self.purchase_validated_by = self.env.user

    def action_terminate(self):
        self.state = 'terminated'

    def action_cancel(self):
        self.state = 'cancelled'

class AssetContractClause(models.Model):
    _name = 'asset.contract.clause'
    _description = 'Asset Contract Clause'

    contract_id = fields.Many2one('asset.contract', 'Contrat', required=True, ondelete='cascade')
    clause_type = fields.Selection([
        ('payment', 'Paiement'),
        ('delivery', 'Livraison'),
        ('warranty', 'Garantie'),
        ('maintenance', 'Maintenance'),
        ('other', 'Autre')
    ], required=True, string='Type de Clause')
    name = fields.Char('Nom', required=True)
    description = fields.Text('Description')
    is_fiscal = fields.Boolean('Clause Fiscale')

class AssetContractVerification(models.Model):
    _name = 'asset.contract.verification'
    _description = 'Asset Contract Verification'

    contract_id = fields.Many2one('asset.contract', 'Contrat', required=True, ondelete='cascade')
    verification_type = fields.Selection([
        ('legal', 'Légale'),
        ('financial', 'Financière'),
        ('technical', 'Technique'),
        ('compliance', 'Conformité')
    ], required=True, string='Type de Vérification')
    name = fields.Char('Nom', required=True)
    status = fields.Selection([
        ('pending', 'En Attente'),
        ('in_progress', 'En Cours'),
        ('verified', 'Vérifié'),
        ('rejected', 'Rejeté')
    ], default='pending', string='Statut')
    verified_by = fields.Many2one('res.users', 'Vérifié par')
    verification_date = fields.Date('Date de Vérification')

    def action_verify(self):
        self.status = 'verified'
        self.verified_by = self.env.user
        self.verification_date = fields.Date.today()


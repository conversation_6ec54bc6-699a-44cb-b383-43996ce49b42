<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Demo Asset Categories -->
    <record id="category_computer" model="fixed.asset.category">
        <field name="name">Computer Equipment</field>
        <field name="code">COMP</field>
        <field name="default_depreciation_years">3</field>
        <field name="default_depreciation_method">linear</field>
    </record>

    <record id="category_furniture_demo" model="fixed.asset.category">
        <field name="name">Office Furniture</field>
        <field name="code">FURN</field>
        <field name="default_depreciation_years">10</field>
        <field name="default_depreciation_method">linear</field>
    </record>

    <!-- Demo Assets -->
    <record id="asset_laptop_demo" model="fixed.asset">
        <field name="name">Laptop Dell Inspiron</field>
        <field name="category_id" ref="category_computer"/>
        <field name="purchase_value">1200.00</field>
        <field name="purchase_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
        <field name="depreciation_years">3</field>
        <field name="depreciation_method">linear</field>
        <field name="state">draft</field>
    </record>

    <record id="asset_desk_demo" model="fixed.asset">
        <field name="name">Office Desk</field>
        <field name="category_id" ref="category_furniture_demo"/>
        <field name="purchase_value">500.00</field>
        <field name="purchase_date" eval="(DateTime.now() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
        <field name="depreciation_years">10</field>
        <field name="depreciation_method">linear</field>
        <field name="state">draft</field>
    </record>
</odoo>


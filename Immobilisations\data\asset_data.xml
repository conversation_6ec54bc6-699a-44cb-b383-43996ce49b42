<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Create Chart of Accounts for Assets if not exists -->
    <record id="account_asset_fixed" model="account.account">
        <field name="code">213</field>
        <field name="name">Immobilisations corporelles</field>
        <field name="account_type">asset_fixed</field>
        <field name="reconcile">False</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="account_depreciation_accumulated" model="account.account">
        <field name="code">2813</field>
        <field name="name">Amortissements des immobilisations corporelles</field>
        <field name="account_type">asset_fixed</field>
        <field name="reconcile">False</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="account_depreciation_expense" model="account.account">
        <field name="code">681</field>
        <field name="name">Dotations aux amortissements</field>
        <field name="account_type">expense</field>
        <field name="reconcile">False</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <!-- Catégories d'Immobilisations par Défaut -->
    <record id="asset_category_computer" model="fixed.asset.category">
        <field name="name">Matériel Informatique</field>
        <field name="code">INFO</field>
        <field name="default_depreciation_years">3</field>
        <field name="default_depreciation_method">linear</field>
        <field name="account_asset_id" ref="account_asset_fixed"/>
        <field name="account_depreciation_id" ref="account_depreciation_accumulated"/>
        <field name="account_expense_id" ref="account_depreciation_expense"/>
    </record>

    <record id="asset_category_furniture" model="fixed.asset.category">
        <field name="name">Mobilier</field>
        <field name="code">MOB</field>
        <field name="default_depreciation_years">10</field>
        <field name="default_depreciation_method">linear</field>
        <field name="account_asset_id" ref="account_asset_fixed"/>
        <field name="account_depreciation_id" ref="account_depreciation_accumulated"/>
        <field name="account_expense_id" ref="account_depreciation_expense"/>
    </record>

    <record id="asset_category_vehicle" model="fixed.asset.category">
        <field name="name">Véhicules</field>
        <field name="code">VEHI</field>
        <field name="default_depreciation_years">5</field>
        <field name="default_depreciation_method">degressive</field>
        <field name="account_asset_id" ref="account_asset_fixed"/>
        <field name="account_depreciation_id" ref="account_depreciation_accumulated"/>
        <field name="account_expense_id" ref="account_depreciation_expense"/>
    </record>
</odoo>



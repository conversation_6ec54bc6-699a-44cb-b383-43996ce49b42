from odoo import models, fields, api, tools, _
from dateutil.relativedelta import relativedelta

class AssetSubscription(models.Model):
    _name = 'asset.subscription'
    _description = 'Asset Subscription'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char('Nom de l\'Abonnement', required=True)
    subscription_type = fields.Selection([
        ('software', 'Logiciel'),
        ('service', 'Service'),
        ('maintenance', 'Maintenance'),
        ('support', 'Support')
    ], required=True, string='Type d\'Abonnement')
    supplier_id = fields.Many2one('res.partner', 'Fournisseur')
    start_date = fields.Date('Date de Début', required=True)
    end_date = fields.Date('Date de Fin', required=True)
    monthly_cost = fields.Float('Coût Mensuel')
    annual_cost = fields.Float('Coût Annuel', compute='_compute_annual_cost', store=True)
    auto_renewal = fields.Boolean('Renouvellement Automatique')
    renewal_period = fields.Integer('Période de Renouvellement (mois)', default=12)
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('active', 'Actif'),
        ('expired', 'Expiré'),
        ('cancelled', 'Annulé')
    ], default='draft', string='État')
    
    # Relations
    asset_ids = fields.Many2many('fixed.asset', string='Immobilisations Liées')
    
    # Computed fields
    days_to_expiry = fields.Integer('Jours avant Expiration', compute='_compute_days_to_expiry')

    @api.depends('monthly_cost')
    def _compute_annual_cost(self):
        for subscription in self:
            subscription.annual_cost = subscription.monthly_cost * 12

    @api.depends('end_date')
    def _compute_days_to_expiry(self):
        today = fields.Date.today()
        for subscription in self:
            if subscription.end_date:
                delta = subscription.end_date - today
                subscription.days_to_expiry = delta.days
            else:
                subscription.days_to_expiry = 0

    def action_activate(self):
        self.state = 'active'

    def action_renew(self):
        if self.auto_renewal:
            self.end_date = self.end_date + relativedelta(months=self.renewal_period)

    def action_cancel(self):
        self.state = 'cancelled'

class AssetSubscriptionList(models.Model):
    _name = 'asset.subscription.list'
    _description = 'Asset Subscription List'
    _auto = False

    name = fields.Char('Nom')
    subscription_type = fields.Char('Type')
    supplier_id = fields.Many2one('res.partner', 'Fournisseur')
    start_date = fields.Date('Date de Début')
    end_date = fields.Date('Date de Fin')
    monthly_cost = fields.Float('Coût Mensuel')
    state = fields.Char('État')

    def init(self):
        tools.drop_view_if_exists(self.env.cr, self._table)
        self.env.cr.execute("""
            CREATE OR REPLACE VIEW %s AS (
                SELECT
                    id,
                    name,
                    subscription_type,
                    supplier_id,
                    start_date,
                    end_date,
                    monthly_cost,
                    state
                FROM asset_subscription
                WHERE state = 'active'
            )
        """ % self._table)




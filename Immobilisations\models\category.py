from odoo import models, fields, api

class FixedAssetCategory(models.Model):
    _name = 'fixed.asset.category'
    _description = 'Fixed Asset Category'
    _order = 'name'

    name = fields.Char('Nom de la Catégorie', required=True)
    code = fields.Char('Code', required=True)
    description = fields.Text('Description')
    
    # Default Values
    default_depreciation_years = fields.Integer('Années d\'Amortissement par Défaut', default=5)
    default_depreciation_method = fields.Selection([
        ('linear', 'Linéaire'),
        ('degressive', 'Dégressif'),
        ('accelerated', 'Accéléré')
    ], default='linear', string='Méthode d\'Amortissement par Défaut')
    
    # Statistics
    asset_count = fields.Integer('Nombre d\'Immobilisations', compute='_compute_asset_count')
    
    def _compute_asset_count(self):
        for category in self:
            category.asset_count = self.env['fixed.asset'].search_count([('category_id', '=', category.id)])




from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class FixedAssetCategory(models.Model):
    _name = 'fixed.asset.category'
    _description = 'Fixed Asset Category'
    _order = 'name'
    _rec_name = 'name'

    # Basic Information
    name = fields.Char('Nom de la Catégorie', required=True, tracking=True)
    code = fields.Char('Code', required=True, tracking=True)
    description = fields.Text('Description')
    active = fields.Boolean('Actif', default=True)

    # Account Configuration
    account_asset_id = fields.Many2one(
        'account.account',
        'Compte d\'Immobilisation',
        required=True,
        domain=[('account_type', '=', 'asset_fixed')],
        help="Compte comptable pour les immobilisations de cette catégorie"
    )
    account_depreciation_id = fields.Many2one(
        'account.account',
        'Compte d\'Amortissement',
        required=True,
        domain=[('account_type', '=', 'asset_fixed')],
        help="Compte comptable pour les amortissements cumulés"
    )
    account_expense_id = fields.Many2one(
        'account.account',
        'Compte de Charge',
        required=True,
        domain=[('account_type', '=', 'expense')],
        help="Compte comptable pour les dotations aux amortissements"
    )

    # Default Values for Assets
    default_depreciation_years = fields.Integer(
        'Années d\'Amortissement par Défaut',
        default=5,
        required=True,
        help="Durée d'amortissement par défaut en années"
    )
    default_depreciation_method = fields.Selection([
        ('linear', 'Linéaire'),
        ('degressive', 'Dégressif'),
        ('accelerated', 'Accéléré')
    ], default='linear', string='Méthode d\'Amortissement par Défaut', required=True)

    # Statistics and Computed Fields
    asset_count = fields.Integer('Nombre d\'Immobilisations', compute='_compute_asset_count')
    total_asset_value = fields.Float('Valeur Totale des Immobilisations', compute='_compute_total_values')
    total_depreciation = fields.Float('Amortissements Cumulés', compute='_compute_total_values')

    # Color for kanban view
    color = fields.Integer('Couleur')

    @api.depends('asset_ids')
    def _compute_asset_count(self):
        """Compute the number of assets in this category"""
        for category in self:
            category.asset_count = len(category.asset_ids)

    @api.depends('asset_ids.purchase_value', 'asset_ids.current_value')
    def _compute_total_values(self):
        """Compute total values for assets in this category"""
        for category in self:
            assets = category.asset_ids.filtered(lambda a: a.state in ['running', 'disposed'])
            category.total_asset_value = sum(assets.mapped('purchase_value'))
            category.total_depreciation = sum(assets.mapped('purchase_value')) - sum(assets.mapped('current_value'))

    # Reverse relation to assets
    asset_ids = fields.One2many('fixed.asset', 'category_id', 'Immobilisations')

    @api.constrains('default_depreciation_years')
    def _check_depreciation_years(self):
        """Validate depreciation years"""
        for category in self:
            if category.default_depreciation_years <= 0:
                raise ValidationError(_("La durée d'amortissement doit être supérieure à 0."))
            if category.default_depreciation_years > 50:
                raise ValidationError(_("La durée d'amortissement ne peut pas dépasser 50 ans."))

    @api.constrains('code')
    def _check_code_unique(self):
        """Ensure category code is unique"""
        for category in self:
            if category.code:
                existing = self.search([
                    ('code', '=', category.code),
                    ('id', '!=', category.id)
                ])
                if existing:
                    raise ValidationError(_("Le code '%s' est déjà utilisé par une autre catégorie.") % category.code)

    def name_get(self):
        """Custom name display"""
        result = []
        for category in self:
            name = category.name
            if category.code:
                name = f"[{category.code}] {name}"
            result.append((category.id, name))
        return result

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        """Enhanced search by name or code"""
        args = args or []
        if name:
            categories = self.search([
                '|',
                ('name', operator, name),
                ('code', operator, name)
            ] + args, limit=limit)
            return categories.name_get()
        return super().name_search(name, args, operator, limit)

    def action_view_assets(self):
        """Action to view assets in this category"""
        self.ensure_one()
        return {
            'name': _('Immobilisations - %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'fixed.asset',
            'view_mode': 'tree,form',
            'domain': [('category_id', '=', self.id)],
            'context': {
                'default_category_id': self.id,
                'search_default_category_id': self.id,
            }
        }




from odoo import models, fields, api, _

class FixedAssetCategory(models.Model):
    _name = 'fixed.asset.category'
    _description = 'Fixed Asset Category'
    _order = 'name'

    name = fields.Char('Nom de la Catégorie', required=True)
    code = fields.Char('Code', required=True)
    description = fields.Text('Description')
    
    # Accounting Configuration - Optional
    account_asset_id = fields.Many2one('account.account', 'Compte d\'Immobilisation')
    account_depreciation_id = fields.Many2one('account.account', 'Compte d\'Amortissement')
    account_expense_id = fields.Many2one('account.account', 'Compte de Charge')
    
    # Default Values
    default_depreciation_years = fields.Integer('Années d\'Amortissement par Défaut', default=5)
    default_depreciation_method = fields.Selection([
        ('linear', 'Linéaire'),
        ('degressive', 'Dégressif'),
        ('accelerated', 'Accéléré')
    ], default='linear', string='Méthode d\'Amortissement par Défaut')
    
    # Statistics
    asset_count = fields.Integer('Nombre d\'Immobilisations', compute='_compute_asset_count')
    
    @api.depends('asset_ids')
    def _compute_asset_count(self):
        for category in self:
            category.asset_count = self.env['fixed.asset'].search_count([('category_id', '=', category.id)])

    @api.constrains('account_asset_id', 'account_depreciation_id', 'account_expense_id')
    def _check_accounts(self):
        for category in self:
            if category.asset_count > 0:  # Only validate if category has assets
                if not category.account_asset_id:
                    raise ValidationError(_('Asset account is required for categories with assets.'))
                if not category.account_depreciation_id:
                    raise ValidationError(_('Depreciation account is required for categories with assets.'))
                if not category.account_expense_id:
                    raise ValidationError(_('Expense account is required for categories with assets.'))







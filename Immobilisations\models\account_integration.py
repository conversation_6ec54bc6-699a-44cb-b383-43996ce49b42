from odoo import models, fields, api, _

class AccountMove(models.Model):
    _inherit = 'account.move'
    
    asset_ids = fields.One2many('fixed.asset', 'invoice_id', 'Related Assets')
    
    def action_post(self):
        """Override to create assets from invoice lines"""
        result = super().action_post()
        
        if self.move_type == 'in_invoice':
            self._create_assets_from_invoice()
        
        return result
    
    def _create_assets_from_invoice(self):
        """Create assets from invoice lines marked as assets"""
        for line in self.invoice_line_ids:
            if line.product_id and line.product_id.is_asset:
                asset_vals = {
                    'name': f"{line.product_id.name} - {self.name}",
                    'product_id': line.product_id.id,
                    'category_id': line.product_id.asset_category_id.id if line.product_id.asset_category_id else False,
                    'purchase_value': line.price_subtotal,
                    'supplier_id': self.partner_id.id,
                    'invoice_id': self.id,
                    'purchase_date': self.invoice_date or fields.Date.today(),
                    'depreciation_method': line.product_id.asset_category_id.default_depreciation_method if line.product_id.asset_category_id else 'linear',
                    'depreciation_years': line.product_id.asset_category_id.default_depreciation_years if line.product_id.asset_category_id else 5,
                    'state': 'draft',
                }
                
                asset = self.env['fixed.asset'].create(asset_vals)
                line.asset_id = asset.id

class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'
    
    asset_id = fields.Many2one('fixed.asset', 'Related Asset')
    
    def create_asset(self):
        """Create asset from invoice line"""
        if not self.product_id or not self.product_id.is_asset:
            return False
        
        asset_vals = {
            'name': f"{self.product_id.name} - {self.move_id.name}",
            'product_id': self.product_id.id,
            'category_id': self.product_id.asset_category_id.id if self.product_id.asset_category_id else False,
            'purchase_value': self.price_subtotal,
            'supplier_id': self.move_id.partner_id.id,
            'invoice_id': self.move_id.id,
            'purchase_date': self.move_id.invoice_date or fields.Date.today(),
            'depreciation_method': self.product_id.asset_category_id.default_depreciation_method if self.product_id.asset_category_id else 'linear',
            'depreciation_years': self.product_id.asset_category_id.default_depreciation_years if self.product_id.asset_category_id else 5,
            'state': 'draft',
        }
        
        asset = self.env['fixed.asset'].create(asset_vals)
        self.asset_id = asset.id
        return asset

class ProductTemplate(models.Model):
    _inherit = 'product.template'
    
    is_asset = fields.Boolean('Is Asset', default=False, help="Check this if this product should create a fixed asset when purchased")
    asset_category_id = fields.Many2one('fixed.asset.category', 'Default Asset Category', help="Default category for assets created from this product")


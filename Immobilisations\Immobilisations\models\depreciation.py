from odoo import models, fields, api, _

class AssetDepreciationLine(models.Model):
    _name = 'asset.depreciation.line'
    _description = 'Asset Depreciation Line'
    _order = 'date'

    name = fields.Char('Nom', required=True)
    asset_id = fields.Many2one('fixed.asset', 'Immobilisation', required=True, ondelete='cascade')
    date = fields.Date('Date', required=True)
    depreciation_amount = fields.Float('Montant d\'Amortissement', required=True)
    cumulative_depreciation = fields.Float('Amortissement Cumulé', compute='_compute_cumulative')
    remaining_value = fields.Float('Valeur Restante', compute='_compute_remaining')
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('posted', 'Comptabilisé'),
        ('cancelled', 'Annulé')
    ], default='draft', string='État')
    move_id = fields.Many2one('account.move', 'Écriture Comptable')

    @api.depends('asset_id.depreciation_line_ids', 'depreciation_amount')
    def _compute_cumulative(self):
        for line in self:
            previous_lines = line.asset_id.depreciation_line_ids.filtered(
                lambda l: l.date <= line.date and l.state == 'posted' and l.id != line.id
            )
            line.cumulative_depreciation = sum(previous_lines.mapped('depreciation_amount')) + line.depreciation_amount

    @api.depends('asset_id.purchase_value', 'cumulative_depreciation')
    def _compute_remaining(self):
        for line in self:
            line.remaining_value = line.asset_id.purchase_value - line.cumulative_depreciation

    def action_post(self):
        self.state = 'posted'
        # Create accounting entry logic here

class AssetDepreciationWizard(models.TransientModel):
    _name = 'asset.depreciation.wizard'
    _description = 'Asset Depreciation Wizard'
    
    date_from = fields.Date('From Date', required=True, default=fields.Date.today)
    date_to = fields.Date('To Date', required=True, default=fields.Date.today)
    category_ids = fields.Many2many('fixed.asset.category', 'Categories')
    
    def action_post_depreciation(self):
        """Post all depreciation lines in date range"""
        domain = [
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to),
            ('state', '=', 'draft')
        ]
        
        if self.category_ids:
            domain.append(('asset_id.category_id', 'in', self.category_ids.ids))
        
        lines = self.env['asset.depreciation.line'].search(domain)
        lines.action_post()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('%s depreciation lines posted successfully.') % len(lines),
                'type': 'success'
            }
        }






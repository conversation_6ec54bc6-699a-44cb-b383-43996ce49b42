from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class FixedAsset(models.Model):
    _name = 'fixed.asset'
    _description = 'Fixed Asset'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'code, name'

    # Basic Information
    name = fields.Char('Nom de l\'Immobilisation', required=True, tracking=True)
    code = fields.Char('Code Immobilisation', required=True, copy=False, default=lambda self: _('Nouveau'))
    category_id = fields.Many2one('fixed.asset.category', 'Catégorie', required=True, tracking=True)
    description = fields.Text('Description')
    product_id = fields.Many2one('product.product', 'Produit')

    # State Management
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('running', 'En Service'),
        ('disposed', 'Cédé'),
        ('sold', 'Vendu')
    ], default='draft', tracking=True, string='Statut')

    # Financial Information
    purchase_value = fields.Float('Valeur d\'Achat', required=True, tracking=True)
    current_value = fields.Float('Valeur Actuelle', compute='_compute_current_value', store=True)
    
    # Dates
    purchase_date = fields.Date('Date d\'Achat', required=True, default=fields.Date.today, tracking=True)
    start_depreciation_date = fields.Date('Date de Début d\'Amortissement')

    @api.model
    def create(self, vals):
        if vals.get('code', _('Nouveau')) == _('Nouveau'):
            vals['code'] = self.env['ir.sequence'].next_by_code('fixed.asset') or _('Nouveau')
        return super().create(vals)

    @api.depends('purchase_value')
    def _compute_current_value(self):
        for asset in self:
            asset.current_value = asset.purchase_value

    def action_validate(self):
        """Validate asset"""
        self.state = 'running'
        if not self.start_depreciation_date:
            self.start_depreciation_date = self.purchase_date

    def action_dispose(self):
        """Dispose asset"""
        self.state = 'disposed'




class FixedAssetCategory(models.Model):
    _name = 'fixed.asset.category'
    _description = 'Fixed Asset Category'
    _order = 'name'

    name = fields.Char('Nom', required=True)
    code = fields.Char('Code')
    
    # Account Configuration
    account_asset_id = fields.Many2one('account.account', 'Compte d\'Immobilisation', required=True)
    account_depreciation_id = fields.Many2one('account.account', 'Compte d\'Amortissement', required=True)
    account_expense_id = fields.Many2one('account.account', 'Compte de Charge', required=True)
    
    # Default Values
    default_depreciation_years = fields.Integer('Années d\'Amortissement par Défaut', default=5)
    default_depreciation_method = fields.Selection([
        ('linear', 'Linéaire'),
        ('degressive', 'Dégressif'),
        ('accelerated', 'Accéléré')
    ], default='linear', string='Méthode d\'Amortissement par Défaut')
    
    # Statistics
    asset_count = fields.Integer('Nombre d\'Immobilisations', compute='_compute_asset_count')
    
    def _compute_asset_count(self):
        for category in self:
            category.asset_count = self.env['fixed.asset'].search_count([('category_id', '=', category.id)])



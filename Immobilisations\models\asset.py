from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class FixedAsset(models.Model):
    _name = 'fixed.asset'
    _description = 'Fixed Asset'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'code, name'

    # Basic Information
    name = fields.Char('Nom de l\'Immobilisation', required=True, tracking=True)
    code = fields.Char('Code Immobilisation', required=True, copy=False, default=lambda self: _('Nouveau'))
    category_id = fields.Many2one('fixed.asset.category', 'Catégorie', required=True, tracking=True)
    description = fields.Text('Description')
    product_id = fields.Many2one('product.product', 'Produit')

    # Purchase Information
    supplier_id = fields.Many2one('res.partner', 'Fournisseur', domain=[('is_company', '=', True)])
    invoice_id = fields.Many2one('account.move', 'Facture d\'Achat')
    contract_id = fields.Many2one('asset.contract', 'Contrat')

    # State Management
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('running', 'En Service'),
        ('disposed', 'Cédé'),
        ('sold', 'Vendu')
    ], default='draft', tracking=True, string='Statut')

    # Financial Information
    purchase_value = fields.Float('Valeur d\'Achat', required=True, tracking=True)
    current_value = fields.Float('Valeur Actuelle', compute='_compute_current_value', store=True)
    
    # Dates
    purchase_date = fields.Date('Date d\'Achat', required=True, default=fields.Date.today, tracking=True)
    start_depreciation_date = fields.Date('Date de Début d\'Amortissement')

    # Depreciation Information
    depreciation_method = fields.Selection([
        ('linear', 'Linéaire'),
        ('degressive', 'Dégressif'),
        ('accelerated', 'Accéléré')
    ], string='Méthode d\'Amortissement', default='linear')
    depreciation_years = fields.Integer('Années d\'Amortissement', default=5)

    # Location Information
    location = fields.Char('Emplacement')
    responsible_id = fields.Many2one('res.users', 'Responsable')

    # Relationships
    depreciation_line_ids = fields.One2many('asset.depreciation.line', 'asset_id', 'Lignes d\'Amortissement')
    movement_ids = fields.One2many('asset.movement', 'asset_id', 'Mouvements')
    maintenance_ids = fields.One2many('asset.maintenance', 'asset_id', 'Maintenances')

    @api.model
    def create(self, vals):
        if vals.get('code', _('Nouveau')) == _('Nouveau'):
            vals['code'] = self.env['ir.sequence'].next_by_code('fixed.asset') or _('Nouveau')
        return super().create(vals)

    @api.depends('purchase_value', 'depreciation_line_ids.accumulated_depreciation')
    def _compute_current_value(self):
        for asset in self:
            total_depreciation = sum(asset.depreciation_line_ids.filtered(lambda l: l.state == 'posted').mapped('depreciation_amount'))
            asset.current_value = asset.purchase_value - total_depreciation

    def action_validate(self):
        """Validate asset"""
        self.state = 'running'
        if not self.start_depreciation_date:
            self.start_depreciation_date = self.purchase_date

    def action_dispose(self):
        """Dispose asset"""
        self.state = 'disposed'

    @api.onchange('category_id')
    def _onchange_category_id(self):
        """Set default values from category"""
        if self.category_id:
            self.depreciation_method = self.category_id.default_depreciation_method
            self.depreciation_years = self.category_id.default_depreciation_years

    def generate_depreciation_lines(self):
        """Generate depreciation lines for this asset"""
        if not self.category_id or not self.purchase_value:
            return

        # Clear existing draft lines
        self.depreciation_line_ids.filtered(lambda l: l.state == 'draft').unlink()

        if self.depreciation_method == 'linear':
            self._generate_linear_depreciation()
        elif self.depreciation_method == 'degressive':
            self._generate_degressive_depreciation()

    def _generate_linear_depreciation(self):
        """Generate linear depreciation lines"""
        annual_depreciation = self.purchase_value / self.depreciation_years
        start_date = self.start_depreciation_date or self.purchase_date

        for year in range(self.depreciation_years):
            depreciation_date = start_date.replace(year=start_date.year + year + 1)
            accumulated = annual_depreciation * (year + 1)
            remaining = self.purchase_value - accumulated

            self.env['asset.depreciation.line'].create({
                'name': f'Amortissement {year + 1}/{self.depreciation_years}',
                'asset_id': self.id,
                'depreciation_date': depreciation_date,
                'depreciation_amount': annual_depreciation,
                'accumulated_depreciation': accumulated,
                'remaining_value': remaining,
            })

    def _generate_degressive_depreciation(self):
        """Generate degressive depreciation lines"""
        # Simplified degressive method
        rate = 2.0 / self.depreciation_years
        remaining_value = self.purchase_value
        start_date = self.start_depreciation_date or self.purchase_date

        for year in range(self.depreciation_years):
            depreciation_amount = remaining_value * rate
            if year == self.depreciation_years - 1:  # Last year
                depreciation_amount = remaining_value

            depreciation_date = start_date.replace(year=start_date.year + year + 1)
            accumulated = self.purchase_value - remaining_value + depreciation_amount

            self.env['asset.depreciation.line'].create({
                'name': f'Amortissement {year + 1}/{self.depreciation_years}',
                'asset_id': self.id,
                'depreciation_date': depreciation_date,
                'depreciation_amount': depreciation_amount,
                'accumulated_depreciation': accumulated,
                'remaining_value': remaining_value - depreciation_amount,
            })

            remaining_value -= depreciation_amount
            if remaining_value <= 0:
                break








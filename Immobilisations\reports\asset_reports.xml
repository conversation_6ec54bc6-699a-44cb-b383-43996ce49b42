<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Rapport Liste des Immobilisations -->
    <record id="action_report_asset_list" model="ir.actions.report">
        <field name="name">Liste des Immobilisations</field>
        <field name="model">fixed.asset</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">Immobilisations.report_asset_list</field>
        <field name="report_file">Immobilisations.report_asset_list</field>
        <field name="binding_model_id" ref="model_fixed_asset"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Template du rapport -->
    <template id="report_asset_list">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <t t-call="web.external_layout">
                    <div class="page">
                        <h2>Liste des Immobilisations</h2>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Nom</th>
                                    <th>Catégorie</th>
                                    <th>Valeur d'Achat</th>
                                    <th>État</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span t-field="o.code"/></td>
                                    <td><span t-field="o.name"/></td>
                                    <td><span t-field="o.category_id"/></td>
                                    <td><span t-field="o.purchase_value"/></td>
                                    <td><span t-field="o.state"/></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </t>
            </t>
        </t>
    </template>
</odoo>


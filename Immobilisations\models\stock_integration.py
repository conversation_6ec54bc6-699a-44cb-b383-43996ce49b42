from odoo import models, fields, api, _

class StockMove(models.Model):
    _inherit = 'stock.move'
    
    asset_id = fields.Many2one('fixed.asset', 'Related Asset')
    is_asset_movement = fields.Boolean('Asset Movement', default=False)

class StockPicking(models.Model):
    _inherit = 'stock.picking'
    
    asset_movement_id = fields.Many2one('asset.movement', 'Asset Movement')
    
    def button_validate(self):
        """Override to update asset location after stock movement"""
        result = super().button_validate()
        
        # Update asset locations based on stock movements
        for move in self.move_ids:
            if move.asset_id:
                move.asset_id.location = move.location_dest_id.complete_name
        
        return result

class ProductProduct(models.Model):
    _inherit = 'product.product'
    
    is_asset = fields.<PERSON><PERSON>an('Is Asset', default=False)
    asset_category_id = fields.Many2one('fixed.asset.category', 'Default Asset Category')
    
    def create_asset_from_product(self, purchase_value, supplier_id=False, invoice_id=False):
        """Create asset from product"""
        if not self.is_asset:
            return False
        
        asset_vals = {
            'name': self.name,
            'product_id': self.id,
            'category_id': self.asset_category_id.id if self.asset_category_id else False,
            'purchase_value': purchase_value,
            'supplier_id': supplier_id,
            'invoice_id': invoice_id,
            'description': self.description or '',
            'depreciation_method': self.asset_category_id.default_depreciation_method if self.asset_category_id else 'linear',
            'depreciation_years': self.asset_category_id.default_depreciation_years if self.asset_category_id else 5,
        }
        
        return self.env['fixed.asset'].create(asset_vals)

class ProductTemplate(models.Model):
    _inherit = 'product.template'
    
    is_asset = fields.Boolean('Is Asset', default=False)
    asset_category_id = fields.Many2one('fixed.asset.category', 'Default Asset Category')

<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue Formulaire Catégorie -->
    <record id="view_fixed_asset_category_form" model="ir.ui.view">
        <field name="name">fixed.asset.category.form</field>
        <field name="model">fixed.asset.category</field>
        <field name="arch" type="xml">
            <form string="Catégorie d'Immobilisation">
                <header>
                    <button name="action_view_assets" type="object" string="Voir les Immobilisations"
                            class="btn-primary" attrs="{'invisible': [('asset_count', '=', 0)]}"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_assets" type="object" class="oe_stat_button" icon="fa-cubes">
                            <field name="asset_count" widget="statinfo" string="Immobilisations"/>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Archivé" bg_color="bg-danger"
                            attrs="{'invisible': [('active', '=', True)]}"/>
                    <group>
                        <group>
                            <field name="active" invisible="1"/>
                            <field name="name"/>
                            <field name="code"/>
                            <field name="color" widget="color"/>
                        </group>
                        <group>
                            <field name="default_depreciation_years"/>
                            <field name="default_depreciation_method"/>
                        </group>
                    </group>
                    <group string="Configuration Comptable">
                        <group>
                            <field name="account_asset_id" options="{'no_create': True}"/>
                            <field name="account_depreciation_id" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="account_expense_id" options="{'no_create': True}"/>
                        </group>
                    </group>
                    <group string="Statistiques" attrs="{'invisible': [('asset_count', '=', 0)]}">
                        <group>
                            <field name="total_asset_value" widget="monetary"/>
                            <field name="total_depreciation" widget="monetary"/>
                        </group>
                    </group>
                    <group>
                        <field name="description" placeholder="Description de la catégorie..."/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Vue Liste Catégorie -->
    <record id="view_fixed_asset_category_tree" model="ir.ui.view">
        <field name="name">fixed.asset.category.tree</field>
        <field name="model">fixed.asset.category</field>
        <field name="arch" type="xml">
            <tree string="Catégories d'Immobilisation" decoration-muted="not active">
                <field name="active" invisible="1"/>
                <field name="color" invisible="1"/>
                <field name="code"/>
                <field name="name"/>
                <field name="default_depreciation_years"/>
                <field name="default_depreciation_method"/>
                <field name="asset_count"/>
                <field name="total_asset_value" widget="monetary" sum="Total"/>
                <field name="account_asset_id"/>
            </tree>
        </field>
    </record>

    <!-- Vue Kanban Catégorie -->
    <record id="view_fixed_asset_category_kanban" model="ir.ui.view">
        <field name="name">fixed.asset.category.kanban</field>
        <field name="model">fixed.asset.category</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <field name="color"/>
                <field name="name"/>
                <field name="code"/>
                <field name="asset_count"/>
                <field name="total_asset_value"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_color_#{kanban_getcolor(record.color.raw_value)} oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                    <small class="o_kanban_record_subtitle text-muted">
                                        <field name="code"/>
                                    </small>
                                </div>
                            </div>
                            <div class="o_kanban_record_body">
                                <div class="row">
                                    <div class="col-6">
                                        <span class="fa fa-cubes"/> <field name="asset_count"/> immobilisations
                                    </div>
                                    <div class="col-6">
                                        <span class="fa fa-money"/> <field name="total_asset_value" widget="monetary"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Vue Recherche Catégorie -->
    <record id="view_fixed_asset_category_search" model="ir.ui.view">
        <field name="name">fixed.asset.category.search</field>
        <field name="model">fixed.asset.category</field>
        <field name="arch" type="xml">
            <search string="Rechercher Catégories">
                <field name="name" string="Nom ou Code" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                <field name="code"/>
                <field name="default_depreciation_method"/>
                <field name="account_asset_id"/>
                <separator/>
                <filter name="active" string="Actif" domain="[('active', '=', True)]" default="1"/>
                <filter name="inactive" string="Archivé" domain="[('active', '=', False)]"/>
                <separator/>
                <filter name="with_assets" string="Avec Immobilisations" domain="[('asset_count', '>', 0)]"/>
                <filter name="without_assets" string="Sans Immobilisations" domain="[('asset_count', '=', 0)]"/>
                <separator/>
                <filter name="linear" string="Amortissement Linéaire" domain="[('default_depreciation_method', '=', 'linear')]"/>
                <filter name="degressive" string="Amortissement Dégressif" domain="[('default_depreciation_method', '=', 'degressive')]"/>
                <group expand="0" string="Grouper par">
                    <filter name="group_depreciation_method" string="Méthode d'Amortissement"
                            context="{'group_by': 'default_depreciation_method'}"/>
                    <filter name="group_account" string="Compte d'Immobilisation"
                            context="{'group_by': 'account_asset_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action Catégorie -->
    <record id="action_fixed_asset_category" model="ir.actions.act_window">
        <field name="name">Catégories d'Immobilisation</field>
        <field name="res_model">fixed.asset.category</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" ref="view_fixed_asset_category_search"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Créer une nouvelle catégorie d'immobilisation
            </p>
            <p>
                Les catégories permettent de classifier vos immobilisations et de définir
                les paramètres comptables et d'amortissement par défaut.
            </p>
        </field>
    </record>
</odoo>


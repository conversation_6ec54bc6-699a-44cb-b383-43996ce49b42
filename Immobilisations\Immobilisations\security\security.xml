<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="module_category_asset_management" model="ir.module.category">
        <field name="name">Gestion des Immobilisations</field>
        <field name="description">Catégorie pour la gestion des immobilisations</field>
        <field name="sequence">20</field>
    </record>

    <record id="group_asset_user" model="res.groups">
        <field name="name">Utilisateur</field>
        <field name="category_id" ref="module_category_asset_management"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="group_asset_manager" model="res.groups">
        <field name="name">Responsable</field>
        <field name="category_id" ref="module_category_asset_management"/>
        <field name="implied_ids" eval="[(4, ref('group_asset_user'))]"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>
</odoo>


from odoo import models, fields, api, _

class AssetMovement(models.Model):
    _name = 'asset.movement'
    _description = 'Asset Movement'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'movement_date desc'

    name = fields.Char('Référence', required=True, copy=False, default=lambda self: _('Nouveau'))
    asset_id = fields.Many2one('fixed.asset', 'Immobilisation', required=True)
    movement_type = fields.Selection([
        ('assignment', 'Affectation'),
        ('transfer', 'Transfert'),
        ('return', 'Retour'),
        ('disposal', 'Cession')
    ], required=True, string='Type de Mouvement')
    movement_date = fields.Date('Date', required=True, default=fields.Date.today)
    from_location = fields.Char('De')
    to_location = fields.Char('Vers')
    responsible_id = fields.Many2one('res.users', 'Responsable')
    notes = fields.Text('Notes')
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('confirmed', 'Confirmé'),
        ('done', 'Terminé')
    ], default='draft', string='État')

    @api.model
    def create(self, vals):
        if vals.get('name', _('Nouveau')) == _('Nouveau'):
            vals['name'] = self.env['ir.sequence'].next_by_code('asset.movement') or _('Nouveau')
        return super().create(vals)

    def action_confirm(self):
        self.state = 'confirmed'

    def action_done(self):
        self.state = 'done'



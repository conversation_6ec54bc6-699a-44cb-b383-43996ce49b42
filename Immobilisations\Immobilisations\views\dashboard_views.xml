<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Asset Search View -->
    <record id="view_fixed_asset_search" model="ir.ui.view">
        <field name="name">fixed.asset.search</field>
        <field name="model">fixed.asset</field>
        <field name="arch" type="xml">
            <search string="Fixed Assets">
                <field name="name"/>
                <field name="code"/>
                <field name="category_id"/>
                <field name="current_holder_id"/>
                <field name="supplier_id"/>
                <filter string="Active" name="active" domain="[('state', '=', 'active')]"/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Disposed" name="disposed" domain="[('state', '=', 'disposed')]"/>
                <separator/>
                <filter string="Assigned" name="assigned" domain="[('current_holder_id', '!=', False)]"/>
                <filter string="Unassigned" name="unassigned" domain="[('current_holder_id', '=', False)]"/>
                <separator/>
                <filter string="Purchase This Month" name="purchase_this_month" 
                        domain="[('purchase_date', '&gt;=', (context_today() - relativedelta(months=1)).strftime('%Y-%m-01')), ('purchase_date', '&lt;', context_today().strftime('%Y-%m-01'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Category" name="group_category" context="{'group_by': 'category_id'}"/>
                    <filter string="Holder" name="group_holder" context="{'group_by': 'current_holder_id'}"/>
                    <filter string="State" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Purchase Date" name="group_purchase_date" context="{'group_by': 'purchase_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Contract Search View -->
    <record id="view_asset_contract_search" model="ir.ui.view">
        <field name="name">asset.contract.search</field>
        <field name="model">asset.contract</field>
        <field name="arch" type="xml">
            <search string="Asset Contracts">
                <field name="name"/>
                <field name="contract_number"/>
                <field name="supplier_id"/>
                <field name="contract_type"/>
                <filter string="Active" name="active" domain="[('state', '=', 'active')]"/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Under Review" name="under_review" domain="[('state', '=', 'under_review')]"/>
                <filter string="Approved" name="approved" domain="[('state', '=', 'approved')]"/>
                <separator/>
                <filter string="Verified" name="verified" domain="[('is_verified', '=', True)]"/>
                <filter string="Purchase Validated" name="purchase_validated" domain="[('purchase_validated', '=', True)]"/>
                <group expand="0" string="Group By">
                    <filter string="Contract Type" name="group_type" context="{'group_by': 'contract_type'}"/>
                    <filter string="Supplier" name="group_supplier" context="{'group_by': 'supplier_id'}"/>
                    <filter string="State" name="group_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Subscription Search View -->
    <record id="view_asset_subscription_search" model="ir.ui.view">
        <field name="name">asset.subscription.search</field>
        <field name="model">asset.subscription</field>
        <field name="arch" type="xml">
            <search string="Asset Subscriptions">
                <field name="name"/>
                <field name="provider_id"/>
                <field name="asset_id"/>
                <field name="subscription_type"/>
                <filter string="Active" name="active" domain="[('state', '=', 'active')]"/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Expired" name="expired" domain="[('state', '=', 'expired')]"/>
                <separator/>
                <filter string="Expiring Soon" name="expiring_soon" domain="[('is_expiring_soon', '=', True)]"/>
                <filter string="Auto Renewal" name="auto_renewal" domain="[('auto_renewal', '=', True)]"/>
                <group expand="0" string="Group By">
                    <filter string="Subscription Type" name="group_type" context="{'group_by': 'subscription_type'}"/>
                    <filter string="Provider" name="group_provider" context="{'group_by': 'provider_id'}"/>
                    <filter string="State" name="group_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>
</odoo>
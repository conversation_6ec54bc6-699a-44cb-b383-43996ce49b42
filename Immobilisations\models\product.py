from odoo import models, fields, api

class ProductTemplate(models.Model):
    _inherit = 'product.template'
    
    is_asset = fields.<PERSON><PERSON><PERSON>('Est une Immobilisation', default=False)
    asset_category_id = fields.Many2one('fixed.asset.category', 'Catégorie d\'Immobilisation')
    
class ProductProduct(models.Model):
    _inherit = 'product.product'
    
    asset_ids = fields.One2many('fixed.asset', 'product_id', 'Immobilisations Créées')
    asset_count = fields.Integer('Nombre d\'Immobilisations', compute='_compute_asset_count')
    
    @api.depends('asset_ids')
    def _compute_asset_count(self):
        for product in self:
            product.asset_count = len(product.asset_ids)
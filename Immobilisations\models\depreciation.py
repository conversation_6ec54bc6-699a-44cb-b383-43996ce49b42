from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class AssetDepreciationLine(models.Model):
    _name = 'asset.depreciation.line'
    _description = 'Asset Depreciation Line'
    _order = 'depreciation_date'

    name = fields.Char('Description', required=True)
    asset_id = fields.Many2one('fixed.asset', 'Immobilisation', required=True, ondelete='cascade')
    depreciation_date = fields.Date('Date d\'Amortissement', required=True)
    depreciation_amount = fields.Float('Montant d\'Amortissement', required=True)
    accumulated_depreciation = fields.Float('Amortissement Cumulé')
    remaining_value = fields.Float('Valeur Résiduelle')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('posted', 'Comptabilisé')
    ], default='draft', string='État')
    
    move_id = fields.Many2one('account.move', 'Écriture Comptable')
    
    def action_post_depreciation(self):
        """Post depreciation entry"""
        for line in self:
            if line.state == 'posted':
                continue
            
            journal = line._get_depreciation_journal()
            
            move_vals = {
                'journal_id': journal.id,
                'date': line.depreciation_date,
                'ref': f'Amortissement: {line.asset_id.name}',
                'line_ids': [
                    (0, 0, {
                        'name': line.name,
                        'account_id': line.asset_id.category_id.account_expense_id.id,
                        'debit': line.depreciation_amount,
                        'credit': 0.0,
                    }),
                    (0, 0, {
                        'name': line.name,
                        'account_id': line.asset_id.category_id.account_depreciation_id.id,
                        'debit': 0.0,
                        'credit': line.depreciation_amount,
                    })
                ]
            }
            
            move = self.env['account.move'].create(move_vals)
            move.action_post()
            
            line.move_id = move.id
            line.state = 'posted'
    
    def _get_depreciation_journal(self):
        """Get depreciation journal"""
        journal = self.env['account.journal'].search([
            ('type', '=', 'general'),
            ('code', '=', 'AMORT')
        ], limit=1)
        
        if not journal:
            journal = self.env['account.journal'].search([('type', '=', 'general')], limit=1)
        
        if not journal:
            raise ValidationError(_('Aucun journal trouvé pour les amortissements.'))
        
        return journal

class AssetDepreciationWizard(models.TransientModel):
    _name = 'asset.depreciation.wizard'
    _description = 'Asset Depreciation Wizard'
    
    date_from = fields.Date('From Date', required=True, default=fields.Date.today)
    date_to = fields.Date('To Date', required=True, default=fields.Date.today)
    category_ids = fields.Many2many('fixed.asset.category', 'Categories')
    
    def action_post_depreciation(self):
        """Post all depreciation lines in date range"""
        domain = [
            ('depreciation_date', '>=', self.date_from),
            ('depreciation_date', '<=', self.date_to),
            ('state', '=', 'draft')
        ]
        
        if self.category_ids:
            domain.append(('asset_id.category_id', 'in', self.category_ids.ids))
        
        lines = self.env['asset.depreciation.line'].search(domain)
        lines.action_post_depreciation()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('%s depreciation lines posted successfully.') % len(lines),
                'type': 'success'
            }
        }





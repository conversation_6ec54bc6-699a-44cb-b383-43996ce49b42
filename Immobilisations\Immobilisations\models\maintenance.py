from odoo import models, fields, api, _

class AssetMaintenance(models.Model):
    _name = 'asset.maintenance'
    _description = 'Asset Maintenance'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char('Référence', required=True, copy=False, default=lambda self: _('Nouveau'))
    asset_id = fields.Many2one('fixed.asset', 'Immobilisation', required=True)
    maintenance_type = fields.Selection([
        ('preventive', 'Préventive'),
        ('corrective', 'Corrective'),
        ('emergency', 'Urgence')
    ], required=True, string='Type de Maintenance')
    scheduled_date = fields.Date('Date Prévue')
    completion_date = fields.Date('Date de Réalisation')
    technician_id = fields.Many2one('res.users', 'Technicien')
    cost = fields.Float('Coût')
    description = fields.Text('Description')
    state = fields.Selection([
        ('scheduled', 'Planifiée'),
        ('in_progress', 'En Cours'),
        ('completed', 'Terminée'),
        ('cancelled', 'Annulée')
    ], default='scheduled', string='État')

    @api.model
    def create(self, vals):
        if vals.get('name', _('Nouveau')) == _('Nouveau'):
            vals['name'] = self.env['ir.sequence'].next_by_code('asset.maintenance') or _('Nouveau')
        return super().create(vals)

    def action_start(self):
        self.state = 'in_progress'

    def action_complete(self):
        self.state = 'completed'
        self.completion_date = fields.Date.today()

    def action_cancel(self):
        self.state = 'cancelled'

class AssetMaintenanceSchedule(models.Model):
    _name = 'asset.maintenance.schedule'
    _description = 'Asset Maintenance Schedule'
    
    asset_id = fields.Many2one('fixed.asset', 'Asset', required=True)
    maintenance_type = fields.Selection([
        ('preventive', 'Preventive'),
        ('corrective', 'Corrective')
    ], required=True, default='preventive')
    
    frequency = fields.Selection([
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('semi_annual', 'Semi-Annual'),
        ('annual', 'Annual')
    ], required=True, default='annual')
    
    next_maintenance_date = fields.Date('Next Maintenance Date', required=True)
    description = fields.Text('Description')
    active = fields.Boolean('Active', default=True)
    
    @api.model
    def _cron_create_scheduled_maintenance(self):
        """Create maintenance records for scheduled items"""
        today = fields.Date.today()
        
        schedules = self.search([
            ('next_maintenance_date', '<=', today),
            ('active', '=', True)
        ])
        
        for schedule in schedules:
            # Create maintenance record
            self.env['asset.maintenance'].create({
                'asset_id': schedule.asset_id.id,
                'maintenance_type': schedule.maintenance_type,
                'scheduled_date': schedule.next_maintenance_date,
                'description': schedule.description,
            })
            
            # Update next maintenance date
            if schedule.frequency == 'monthly':
                next_date = schedule.next_maintenance_date.replace(month=schedule.next_maintenance_date.month + 1)
            elif schedule.frequency == 'quarterly':
                next_date = schedule.next_maintenance_date.replace(month=schedule.next_maintenance_date.month + 3)
            elif schedule.frequency == 'semi_annual':
                next_date = schedule.next_maintenance_date.replace(month=schedule.next_maintenance_date.month + 6)
            else:  # annual
                next_date = schedule.next_maintenance_date.replace(year=schedule.next_maintenance_date.year + 1)
            
            schedule.next_maintenance_date = next_date



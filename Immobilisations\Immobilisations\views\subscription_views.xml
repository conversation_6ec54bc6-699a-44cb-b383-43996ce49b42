<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue Formulaire Abonnement -->
    <record id="view_asset_subscription_form" model="ir.ui.view">
        <field name="name">asset.subscription.form</field>
        <field name="model">asset.subscription</field>
        <field name="arch" type="xml">
            <form string="Abonnement d'Immobilisation">
                <header>
                    <button name="action_activate" type="object" string="Activer" 
                            class="oe_highlight" states="draft"/>
                    <button name="action_renew" type="object" string="Renouveler" 
                            states="active"/>
                    <button name="action_cancel" type="object" string="Annuler" 
                            states="draft,active"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="subscription_type"/>
                            <field name="supplier_id"/>
                        </group>
                        <group>
                            <field name="start_date"/>
                            <field name="end_date"/>
                            <field name="monthly_cost"/>
                            <field name="annual_cost"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="auto_renewal"/>
                            <field name="renewal_period"/>
                        </group>
                        <group>
                            <field name="days_to_expiry"/>
                        </group>
                    </group>
                    <field name="asset_ids"/>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Vue Liste Abonnement -->
    <record id="view_asset_subscription_tree" model="ir.ui.view">
        <field name="name">asset.subscription.tree</field>
        <field name="model">asset.subscription</field>
        <field name="arch" type="xml">
            <tree string="Abonnements d'Immobilisation">
                <field name="name"/>
                <field name="subscription_type"/>
                <field name="supplier_id"/>
                <field name="monthly_cost"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Action Abonnement -->
    <record id="action_asset_subscription" model="ir.actions.act_window">
        <field name="name">Abonnements d'Immobilisation</field>
        <field name="res_model">asset.subscription</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>



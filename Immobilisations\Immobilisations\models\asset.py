from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class FixedAsset(models.Model):
    _name = 'fixed.asset'
    _description = 'Fixed Asset'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'code, name'

    # Basic Information
    name = fields.Char('Nom de l\'Immobilisation', required=True, tracking=True)
    code = fields.Char('Code Immobilisation', required=True, copy=False, default=lambda self: _('Nouveau'))
    category_id = fields.Many2one('fixed.asset.category', 'Catégorie', required=True, tracking=True)
    description = fields.Text('Description')
    product_id = fields.Many2one('product.product', 'Produit')
    
    # Reference to invoice - FIXED: Changed field name to avoid conflict
    source_move_id = fields.Many2one('account.move', 'Facture Source', readonly=True)

    # State Management
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('running', 'En Service'),
        ('disposed', 'Cédé'),
        ('sold', 'Vendu')
    ], default='draft', tracking=True, string='Statut')

    # Financial Information
    purchase_value = fields.Float('Valeur d\'Achat', required=True, tracking=True)
    current_value = fields.Float('Valeur Actuelle', compute='_compute_current_value', store=True)
    residual_value = fields.Float('Valeur Résiduelle')
    
    # Dates
    purchase_date = fields.Date('Date d\'Achat', required=True, default=fields.Date.today, tracking=True)
    start_depreciation_date = fields.Date('Date de Début d\'Amortissement')
    disposal_date = fields.Date('Date de Cession')
    
    # Location and Assignment
    location = fields.Char('Localisation')
    holder_id = fields.Many2one('hr.employee', 'Détenteur')
    
    # Depreciation
    depreciation_method = fields.Selection([
        ('linear', 'Linéaire'),
        ('degressive', 'Dégressif'),
        ('accelerated', 'Accéléré')
    ], default='linear', required=True, string='Méthode d\'Amortissement')
    depreciation_years = fields.Integer('Années d\'Amortissement', required=True, default=5)
    
    # Relations
    contract_id = fields.Many2one('asset.contract', 'Contrat')
    depreciation_line_ids = fields.One2many('asset.depreciation.line', 'asset_id', 'Lignes d\'Amortissement')
    
    @api.model
    def create(self, vals):
        if vals.get('code', _('Nouveau')) == _('Nouveau'):
            vals['code'] = self.env['ir.sequence'].next_by_code('fixed.asset') or _('Nouveau')
        return super().create(vals)
    
    @api.depends('purchase_value', 'depreciation_line_ids.depreciation_amount')
    def _compute_current_value(self):
        for asset in self:
            total_depreciation = sum(line.depreciation_amount for line in asset.depreciation_line_ids if line.state == 'posted')
            asset.current_value = asset.purchase_value - total_depreciation
    
    def action_validate(self):
        self.state = 'running'
        self._generate_depreciation_lines()
    
    def action_dispose(self):
        self.state = 'disposed'
        self.disposal_date = fields.Date.today()
    
    def _generate_depreciation_lines(self):
        """Generate depreciation lines logic"""
        if not self.category_id.account_depreciation_id:
            return  # Skip if no depreciation account configured
            
        # Clear existing draft lines
        self.depreciation_line_ids.filtered(lambda l: l.state == 'draft').unlink()
        
        if self.depreciation_years <= 0 or self.purchase_value <= 0:
            return
            
        annual_depreciation = (self.purchase_value - self.residual_value) / self.depreciation_years
        monthly_depreciation = annual_depreciation / 12
        
        start_date = self.start_depreciation_date or self.purchase_date
        
        for year in range(self.depreciation_years):
            for month in range(12):
                line_date = start_date.replace(year=start_date.year + year, month=month + 1)
                
                self.env['asset.depreciation.line'].create({
                    'asset_id': self.id,
                    'date': line_date,
                    'depreciation_amount': monthly_depreciation,
                    'state': 'draft',
                })




class FixedAssetCategory(models.Model):
    _name = 'fixed.asset.category'
    _description = 'Fixed Asset Category'
    _order = 'name'

    name = fields.Char('Nom', required=True)
    code = fields.Char('Code')
    
    # Account Configuration
    account_asset_id = fields.Many2one('account.account', 'Compte d\'Immobilisation', required=True)
    account_depreciation_id = fields.Many2one('account.account', 'Compte d\'Amortissement', required=True)
    account_expense_id = fields.Many2one('account.account', 'Compte de Charge', required=True)
    
    # Default Values
    default_depreciation_years = fields.Integer('Années d\'Amortissement par Défaut', default=5)
    default_depreciation_method = fields.Selection([
        ('linear', 'Linéaire'),
        ('degressive', 'Dégressif'),
        ('accelerated', 'Accéléré')
    ], default='linear', string='Méthode d\'Amortissement par Défaut')
    
    # Statistics
    asset_count = fields.Integer('Nombre d\'Immobilisations', compute='_compute_asset_count')
    
    def _compute_asset_count(self):
        for category in self:
            category.asset_count = self.env['fixed.asset'].search_count([('category_id', '=', category.id)])






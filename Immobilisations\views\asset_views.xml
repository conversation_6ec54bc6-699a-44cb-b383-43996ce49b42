<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue Formulaire Immobilisation -->
    <record id="view_fixed_asset_form" model="ir.ui.view">
        <field name="name">fixed.asset.form</field>
        <field name="model">fixed.asset</field>
        <field name="arch" type="xml">
            <form string="Immobilisation">
                <header>
                    <button name="action_validate" type="object" string="Valider"
                            class="oe_highlight" states="draft"/>
                    <button name="generate_depreciation_lines" type="object" string="Générer Amortissements"
                            class="btn-secondary" states="running"/>
                    <button name="action_dispose" type="object" string="Céder"
                            states="running"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="code" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="category_id"/>
                            <field name="product_id"/>
                            <field name="supplier_id"/>
                        </group>
                        <group>
                            <field name="purchase_value"/>
                            <field name="current_value"/>
                            <field name="purchase_date"/>
                            <field name="start_depreciation_date"/>
                        </group>
                    </group>
                    <group string="Amortissement">
                        <group>
                            <field name="depreciation_method"/>
                            <field name="depreciation_years"/>
                        </group>
                        <group>
                            <field name="location"/>
                            <field name="responsible_id"/>
                        </group>
                    </group>
                    <group>
                        <field name="description"/>
                    </group>
                    <notebook>
                        <page string="Lignes d'Amortissement">
                            <field name="depreciation_line_ids">
                                <tree>
                                    <field name="depreciation_date"/>
                                    <field name="depreciation_amount"/>
                                    <field name="accumulated_depreciation"/>
                                    <field name="remaining_value"/>
                                    <field name="state"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Mouvements">
                            <field name="movement_ids">
                                <tree>
                                    <field name="movement_date"/>
                                    <field name="movement_type"/>
                                    <field name="from_location"/>
                                    <field name="to_location"/>
                                    <field name="state"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Maintenances">
                            <field name="maintenance_ids">
                                <tree>
                                    <field name="scheduled_date"/>
                                    <field name="maintenance_type"/>
                                    <field name="technician_id"/>
                                    <field name="cost"/>
                                    <field name="state"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Vue Liste Immobilisation -->
    <record id="view_fixed_asset_tree" model="ir.ui.view">
        <field name="name">fixed.asset.tree</field>
        <field name="model">fixed.asset</field>
        <field name="arch" type="xml">
            <tree string="Immobilisations" decoration-muted="state == 'disposed'">
                <field name="code"/>
                <field name="name"/>
                <field name="category_id"/>
                <field name="purchase_value" sum="Total"/>
                <field name="current_value" sum="Total"/>
                <field name="purchase_date"/>
                <field name="location"/>
                <field name="responsible_id"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Vue Recherche Immobilisation -->
    <record id="view_fixed_asset_search" model="ir.ui.view">
        <field name="name">fixed.asset.search</field>
        <field name="model">fixed.asset</field>
        <field name="arch" type="xml">
            <search string="Rechercher Immobilisations">
                <field name="name" string="Nom ou Code" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                <field name="category_id"/>
                <field name="supplier_id"/>
                <field name="responsible_id"/>
                <field name="location"/>
                <separator/>
                <filter name="draft" string="Brouillon" domain="[('state', '=', 'draft')]"/>
                <filter name="running" string="En Service" domain="[('state', '=', 'running')]" default="1"/>
                <filter name="disposed" string="Cédé" domain="[('state', '=', 'disposed')]"/>
                <separator/>
                <filter name="this_year" string="Cette Année" domain="[('purchase_date', '>=', datetime.datetime.now().strftime('%Y-01-01'))]"/>
                <group expand="0" string="Grouper par">
                    <filter name="group_category" string="Catégorie" context="{'group_by': 'category_id'}"/>
                    <filter name="group_state" string="État" context="{'group_by': 'state'}"/>
                    <filter name="group_responsible" string="Responsable" context="{'group_by': 'responsible_id'}"/>
                    <filter name="group_purchase_date" string="Date d'Achat" context="{'group_by': 'purchase_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action Immobilisation -->
    <record id="action_fixed_asset" model="ir.actions.act_window">
        <field name="name">Immobilisations</field>
        <field name="res_model">fixed.asset</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_fixed_asset_search"/>
        <field name="context">{'search_default_running': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Créer une nouvelle immobilisation
            </p>
            <p>
                Les immobilisations représentent les biens durables de votre entreprise.
                Vous pouvez suivre leur valeur, leur amortissement et leur localisation.
            </p>
        </field>
    </record>
</odoo>



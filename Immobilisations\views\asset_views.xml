<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue Formulaire Immobilisation -->
    <record id="view_fixed_asset_form" model="ir.ui.view">
        <field name="name">fixed.asset.form</field>
        <field name="model">fixed.asset</field>
        <field name="arch" type="xml">
            <form string="Immobilisation">
                <header>
                    <button name="action_validate" type="object" string="Valider" 
                            class="oe_highlight" states="draft"/>
                    <button name="action_dispose" type="object" string="Céder" 
                            states="running"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="code" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="category_id"/>
                            <field name="product_id"/>
                        </group>
                        <group>
                            <field name="purchase_value"/>
                            <field name="current_value"/>
                            <field name="purchase_date"/>
                            <field name="start_depreciation_date"/>
                        </group>
                    </group>
                    <group>
                        <field name="description"/>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Vue Liste Immobilisation -->
    <record id="view_fixed_asset_tree" model="ir.ui.view">
        <field name="name">fixed.asset.tree</field>
        <field name="model">fixed.asset</field>
        <field name="arch" type="xml">
            <tree string="Immobilisations">
                <field name="code"/>
                <field name="name"/>
                <field name="category_id"/>
                <field name="purchase_value"/>
                <field name="current_value"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Action Immobilisation -->
    <record id="action_fixed_asset" model="ir.actions.act_window">
        <field name="name">Immobilisations</field>
        <field name="res_model">fixed.asset</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>



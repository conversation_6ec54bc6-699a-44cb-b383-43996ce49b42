from odoo import models, fields, api, _

class AssetMovement(models.Model):
    _name = 'asset.movement'
    _description = 'Asset Movement'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'date desc, id desc'

    name = fields.Char('Référence', required=True, copy=False, default=lambda self: _('Nouveau'))
    date = fields.Date('Date', required=True, default=fields.Date.today)
    asset_id = fields.Many2one('fixed.asset', 'Immobilisation', required=True)
    source_location = fields.Char('Emplacement Source', related='asset_id.location', readonly=True)
    destination_location = fields.Char('Emplacement Destination', required=True)
    responsible_id = fields.Many2one('res.users', 'Responsable', default=lambda self: self.env.user)
    notes = fields.Text('Notes')
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('confirmed', 'Confirmé'),
        ('done', 'Terminé'),
        ('cancelled', 'Annulé')
    ], default='draft', string='État', tracking=True)
    
    @api.model
    def create(self, vals):
        if vals.get('name', _('Nouveau')) == _('Nouveau'):
            vals['name'] = self.env['ir.sequence'].next_by_code('asset.movement') or _('Nouveau')
        return super().create(vals)
    
    def action_confirm(self):
        self.state = 'confirmed'
    
    def action_done(self):
        self.state = 'done'
        self.asset_id.location = self.destination_location
    
    def action_cancel(self):
        self.state = 'cancelled'


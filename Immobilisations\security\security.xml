<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Groupes de Sécurité -->
    <record id="group_asset_user" model="res.groups">
        <field name="name">Utilisateur Immobilisations</field>
        <field name="category_id" ref="base.module_category_administration"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="group_asset_manager" model="res.groups">
        <field name="name">Gestionnaire Immobilisations</field>
        <field name="category_id" ref="base.module_category_administration"/>
        <field name="implied_ids" eval="[(4, ref('group_asset_user'))]"/>
    </record>
</odoo>




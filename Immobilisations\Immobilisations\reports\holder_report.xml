<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Holder Report Template -->
    <template id="report_holder_document">
        <t t-call="web.html_container">
            <div class="page">
                <h2>Asset Holder Report</h2>
                <p><strong>Holder:</strong> <span t-esc="holder_name"/></p>
                <p><strong>Report Date:</strong> <span t-esc="report_date"/></p>
                
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Asset Code</th>
                            <th>Asset Name</th>
                            <th>Category</th>
                            <th>Assignment Date</th>
                            <th>Current Value</th>
                            <th>Location</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="assets" t-as="asset">
                            <tr>
                                <td><span t-field="asset.code"/></td>
                                <td><span t-field="asset.name"/></td>
                                <td><span t-field="asset.category_id.name"/></td>
                                <td><span t-field="asset.purchase_date"/></td>
                                <td><span t-field="asset.current_value" t-options="{'widget': 'monetary'}"/></td>
                                <td><span t-field="asset.location"/></td>
                                <td><span t-field="asset.state"/></td>
                            </tr>
                        </t>
                    </tbody>
                </table>
                
                <div class="row mt32">
                    <div class="col-6">
                        <p><strong>Total Assets:</strong> <span t-esc="len(assets)"/></p>
                        <p><strong>Total Value:</strong> <span t-esc="sum(asset.current_value for asset in assets)"/></p>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- Holder Report Action -->
    <record id="action_report_holder" model="ir.actions.report">
        <field name="name">Holder Report</field>
        <field name="model">hr.employee</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">Immobilisations.report_holder_document</field>
        <field name="report_file">Immobilisations.report_holder_document</field>
    </record>
</odoo>





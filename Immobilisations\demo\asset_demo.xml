<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Demo Asset Categories -->
    <record id="category_computer" model="fixed.asset.category">
        <field name="name">Computer Equipment</field>
        <field name="code">COMP</field>
        <field name="default_depreciation_years">3</field>
        <field name="default_depreciation_method">linear</field>
        <field name="account_asset_id" ref="account.a_fixed_asset"/>
        <field name="account_depreciation_id" ref="account.a_accumulated_depreciation"/>
        <field name="account_expense_id" ref="account.a_expense"/>
    </record>

    <record id="category_furniture" model="fixed.asset.category">
        <field name="name">Office Furniture</field>
        <field name="code">FURN</field>
        <field name="default_depreciation_years">5</field>
        <field name="default_depreciation_method">linear</field>
        <field name="account_asset_id" ref="account.a_fixed_asset"/>
        <field name="account_depreciation_id" ref="account.a_accumulated_depreciation"/>
        <field name="account_expense_id" ref="account.a_expense"/>
    </record>

    <!-- Demo Assets -->
    <record id="asset_laptop_001" model="fixed.asset">
        <field name="name">Dell Laptop XPS 13</field>
        <field name="category_id" ref="category_computer"/>
        <field name="purchase_value">1200.00</field>
        <field name="purchase_date" eval="(DateTime.today() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
        <field name="start_depreciation_date" eval="(DateTime.today() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
        <field name="depreciation_method">linear</field>
        <field name="depreciation_years">3</field>
        <field name="state">active</field>
    </record>

    <record id="asset_desk_001" model="fixed.asset">
        <field name="name">Executive Desk</field>
        <field name="category_id" ref="category_furniture"/>
        <field name="purchase_value">800.00</field>
        <field name="purchase_date" eval="(DateTime.today() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
        <field name="start_depreciation_date" eval="(DateTime.today() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
        <field name="depreciation_method">linear</field>
        <field name="depreciation_years">5</field>
        <field name="state">active</field>
    </record>
</odoo>